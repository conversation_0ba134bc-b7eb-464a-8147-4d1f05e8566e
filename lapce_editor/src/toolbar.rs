 /// Toolbar modulare per MATRIX IDE, compatibile Floem 0.2.

// Placeholder per View - da implementare con Floem 0.2
pub struct View;

pub trait Toolbar {
    fn add_item(&mut self, item: Box<dyn ToolbarItem>);
    fn remove_item(&mut self, id: &str);
    fn reorder_items(&mut self, order: Vec<String>);
    fn register_plugin(&mut self, plugin: Box<dyn ToolbarPlugin>);
    fn load_config(&mut self, config: ToolbarConfig);
}

pub trait ToolbarItem {
    fn id(&self) -> &str;
    fn render(&self) -> View;
    fn set_shortcut(&mut self, shortcut: Option<String>);
    fn set_theme(&mut self, theme: Option<ToolbarTheme>);
}

pub trait ToolbarPlugin {
    fn extend(&self, toolbar: &mut dyn Toolbar);
}

pub struct ToolbarConfig {
    pub items: Vec<ToolbarItemConfig>,
}

pub struct ToolbarItemConfig {
    pub id: String,
    pub kind: ToolbarItemKind,
    pub label: String,
    pub action: Option<String>,
    pub shortcut: Option<String>,
    pub theme: Option<ToolbarTheme>,
}

pub enum ToolbarItemKind {
    Button,
    Menu { items: Vec<ToolbarItemConfig> },
    Separator,
}

pub struct ToolbarTheme {
    pub color: Option<String>,
    pub background: Option<String>,
    pub font: Option<String>,
}

// Esempio di struct base per Toolbar
pub struct MatrixToolbar {
    pub items: Vec<Box<dyn ToolbarItem>>,
    pub plugins: Vec<Box<dyn ToolbarPlugin>>,
}

impl MatrixToolbar {
    pub fn new(config: ToolbarConfig) -> Self {
        // Costruisce la toolbar dagli item di config
        MatrixToolbar {
            items: config.items.into_iter().map(|item_cfg| {
                Box::new(MatrixToolbarItem::from_config(item_cfg)) as Box<dyn ToolbarItem>
            }).collect(),
            plugins: vec![],
        }
    }
}

// Implementazione concreta del trait Toolbar per MatrixToolbar
impl Toolbar for MatrixToolbar {
    fn add_item(&mut self, item: Box<dyn ToolbarItem>) {
        self.items.push(item);
    }

    fn remove_item(&mut self, id: &str) {
        self.items.retain(|item| item.id() != id);
    }

    fn reorder_items(&mut self, order: Vec<String>) {
        let mut new_items = Vec::with_capacity(self.items.len());
        for oid in order {
            if let Some(pos) = self.items.iter().position(|item| item.id() == oid) {
                new_items.push(self.items.remove(pos));
            }
        }
        // Aggiungi eventuali item non ordinati in fondo
        new_items.extend(self.items.drain(..));
        self.items = new_items;
    }

    fn register_plugin(&mut self, plugin: Box<dyn ToolbarPlugin>) {
        plugin.extend(self);
        self.plugins.push(plugin);
    }

    fn load_config(&mut self, config: ToolbarConfig) {
        self.items = config.items.into_iter().map(|item_cfg| {
            Box::new(MatrixToolbarItem::from_config(item_cfg)) as Box<dyn ToolbarItem>
        }).collect();
    }
}

// Implementazione base di ToolbarItem per MatrixToolbarItem
pub struct MatrixToolbarItem {
    id: String,
    kind: ToolbarItemKind,
    label: String,
    action: Option<String>,
    shortcut: Option<String>,
    theme: Option<ToolbarTheme>,
}

impl MatrixToolbarItem {
    pub fn from_config(cfg: ToolbarItemConfig) -> Self {
        MatrixToolbarItem {
            id: cfg.id,
            kind: cfg.kind,
            label: cfg.label,
            action: cfg.action,
            shortcut: cfg.shortcut,
            theme: cfg.theme,
        }
    }
}

impl ToolbarItem for MatrixToolbarItem {
    fn id(&self) -> &str {
        &self.id
    }
    fn render(&self) -> View {
        // Placeholder: da implementare con Floem 0.2
        View
    }
    fn set_shortcut(&mut self, shortcut: Option<String>) {
        self.shortcut = shortcut;
    }
    fn set_theme(&mut self, theme: Option<ToolbarTheme>) {
        self.theme = theme;
    }
}
