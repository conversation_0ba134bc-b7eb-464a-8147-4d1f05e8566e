//! Test di integrazione per MATRIX UI
//!
//! Questo test verifica che i componenti principali dell'UI funzionino insieme
//! correttamente con i crates matrix-core, matrix-ai, matrix-plugin-loader e matrix-data.

use std::sync::Arc;
use matrix_core::Engine as CoreEngine;
use matrix_ui::{App, UiError};
use matrix_ui::theme::ThemeManager;
use matrix_ui::panels::PanelManager;

#[tokio::test]
async fn test_core_integration() -> Result<(), Box<dyn std::error::Error>> {
    // Test che il CoreEngine si inizializzi correttamente
    let core = CoreEngine::new().await?;
    
    // Verifica che l'EventBus sia funzionante
    let event_bus = core.event_bus();
    assert!(!event_bus.is_null(), "EventBus should be initialized");
    
    // Verifica che il DAG engine sia disponibile
    let dag_engine = core.dag_engine();
    assert!(!dag_engine.is_null(), "DAG engine should be initialized");
    
    Ok(())
}

#[tokio::test]
async fn test_theme_manager_integration() -> Result<(), Box<dyn std::error::Error>> {
    // Test che il ThemeManager funzioni con il core
    let core = Arc::new(CoreEngine::new().await?);
    let theme_manager = Arc::new(ThemeManager::new()?);
    
    // Verifica che il tema di default sia caricato
    let default_theme = theme_manager.get_active_theme();
    assert!(default_theme.is_ok(), "Default theme should be available");
    
    Ok(())
}

#[tokio::test]
async fn test_panel_manager_integration() -> Result<(), Box<dyn std::error::Error>> {
    // Test che il PanelManager funzioni
    let theme_manager = Arc::new(ThemeManager::new()?);
    let panel_manager = PanelManager::new(theme_manager);
    
    // Verifica che il panel manager sia inizializzato
    let panel_ids = panel_manager.get_panel_ids()?;
    assert!(panel_ids.is_empty(), "Panel manager should start empty");
    
    Ok(())
}

#[tokio::test]
async fn test_dag_viewer_creation() -> Result<(), Box<dyn std::error::Error>> {
    // Test che il DAG viewer possa essere creato
    let core = Arc::new(CoreEngine::new().await?);
    let theme_manager = Arc::new(ThemeManager::new()?);
    
    // Crea il DAG viewer
    let dag_viewer = matrix_ui::components::dag_viewer::DagViewer::new(
        core.clone(),
        theme_manager.clone()
    )?;
    
    // Verifica che sia stato creato correttamente
    assert!(dag_viewer.current_graph_id().is_none(), "DAG viewer should start with no graph");
    
    Ok(())
}

#[tokio::test]
async fn test_full_app_initialization() -> Result<(), Box<dyn std::error::Error>> {
    // Test che l'App completa possa essere inizializzata
    // Nota: questo test potrebbe fallire a causa degli errori di compilazione rimanenti,
    // ma serve per verificare l'integrazione una volta risolti
    
    let core = Arc::new(CoreEngine::new().await?);
    
    // Per ora testiamo solo che il core sia funzionante
    // Una volta risolti gli errori UI, questo test può essere espanso
    assert!(!core.event_bus().is_null(), "Core should be fully functional");
    
    Ok(())
}

// Test helper per verificare che i trait siano implementati correttamente
#[test]
fn test_error_types() {
    // Verifica che UiError implementi i trait necessari
    let error = UiError::InitializationError("test".to_string());
    let error_string = format!("{}", error);
    assert!(error_string.contains("test"), "Error should format correctly");
    
    // Verifica che l'errore possa essere convertito
    let generic_error: Box<dyn std::error::Error> = Box::new(error);
    assert!(generic_error.to_string().contains("test"), "Error should convert correctly");
}

#[test]
fn test_theme_serialization() {
    // Test che i temi possano essere serializzati/deserializzati
    use matrix_ui::theme::{ThemeColors, Theme};
    use floem::style::Color;
    
    let colors = ThemeColors {
        background: Color::rgb8(0x21, 0x21, 0x21),
        background_secondary: Color::rgb8(0x2D, 0x2D, 0x2D),
        background_tertiary: Color::rgb8(0x3D, 0x3D, 0x3D),
        text: Color::rgb8(0xE0, 0xE0, 0xE0),
        text_secondary: Color::rgb8(0xA0, 0xA0, 0xA0),
        text_tertiary: Color::rgb8(0x80, 0x80, 0x80),
        accent: Color::rgb8(0x00, 0x7A, 0xCC),
        accent_secondary: Color::rgb8(0x00, 0x5A, 0x9C),
        border: Color::rgb8(0x40, 0x40, 0x40),
        error: Color::rgb8(0xF4, 0x43, 0x36),
        warning: Color::rgb8(0xFF, 0x98, 0x00),
        success: Color::rgb8(0x4C, 0xAF, 0x50),
        info: Color::rgb8(0x21, 0x96, 0xF3),
        selection: Color::rgb8(0x26, 0x4F, 0x78),
        highlight: Color::rgb8(0xFF, 0xFF, 0x00),
    };
    
    // Verifica che i colori siano impostati correttamente
    assert_eq!(colors.background, Color::rgb8(0x21, 0x21, 0x21));
    assert_eq!(colors.text, Color::rgb8(0xE0, 0xE0, 0xE0));
}
