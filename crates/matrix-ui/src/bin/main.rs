/// MATRIX_IDE - Main Entry Point
/// 
/// Questo è il punto di ingresso principale per MATRIX_IDE.
/// Avvia l'applicazione con tutte le funzionalità integrate.

use std::sync::Arc;
use tokio;
use log::{info, error};
use matrix_ui::{App, UiError};
use matrix_core::Engine as CoreEngine;

fn main() {
    // Inizializza il logging
    env_logger::Builder::from_default_env()
        .filter_level(log::LevelFilter::Info)
        .init();

    info!("🚀 Avvio MATRIX_IDE...");

    // Su macOS, l'EventLoop deve essere creato sul thread principale
    // Quindi eseguiamo l'inizializzazione asincrona in un runtime separato
    // ma l'UI sul thread principale
    let rt = tokio::runtime::Runtime::new().expect("Failed to create Tokio runtime");

    let result = rt.block_on(async {
        // Inizializza l'applicazione UI (include il core engine)
        info!("🎨 Inizializzazione MATRIX_IDE...");
        let mut app = match App::new() {
            Ok(app) => {
                info!("✅ App creata con successo");
                app
            }
            Err(e) => {
                error!("❌ Errore nella creazione dell'app: {}", e);
                return Err(e);
            }
        };

        // Inizializza tutti i componenti
        info!("⚙️ Inizializzazione componenti...");
        app.initialize().await?;
        info!("✅ Componenti inizializzati con successo");

        info!("🌟 MATRIX_IDE avviato con successo!");
        info!("📋 Funzionalità disponibili:");
        info!("   • DAG Viewer - Visualizzazione grafi interattiva");
        info!("   • AI Panel - Comunicazione con agenti AI");
        info!("   • Plugin System - Sistema di plugin modulare");
        info!("   • Theme Manager - Gestione temi personalizzati");
        info!("   • God Mode Panel - Funzionalità avanzate");

        Ok(app)
    });

    match result {
        Ok(app) => {
            // Avvia l'applicazione sul thread principale (necessario per macOS)
            info!("🎯 Avvio interfaccia grafica...");
            if let Err(e) = app.run() {
                error!("❌ Errore durante l'esecuzione dell'UI: {}", e);
                std::process::exit(1);
            }
        }
        Err(e) => {
            error!("❌ Errore durante l'inizializzazione: {}", e);
            std::process::exit(1);
        }
    }

    info!("👋 MATRIX_IDE terminato correttamente");
}


