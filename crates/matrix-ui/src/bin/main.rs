/// MATRIX_IDE - Main Entry Point
/// 
/// Questo è il punto di ingresso principale per MATRIX_IDE.
/// Avvia l'applicazione con tutte le funzionalità integrate.

use std::sync::Arc;
use tokio;
use log::{info, error};
use matrix_ui::{App, UiError};
use matrix_core::Engine as CoreEngine;

#[tokio::main]
async fn main() {
    // Inizializza il logging
    env_logger::Builder::from_default_env()
        .filter_level(log::LevelFilter::Info)
        .init();

    info!("🚀 Avvio MATRIX_IDE...");

    // Inizializza l'applicazione UI (include il core engine)
    info!("🎨 Inizializzazione MATRIX_IDE...");
    let mut app = match App::new() {
        Ok(app) => {
            info!("✅ App creata con successo");
            app
        }
        Err(e) => {
            error!("❌ Errore nella creazione dell'app: {}", e);
            std::process::exit(1);
        }
    };

    // Inizializza tutti i componenti
    info!("⚙️ Inizializzazione componenti...");
    if let Err(e) = app.initialize() {
        error!("❌ Errore nell'inizializzazione dei componenti: {}", e);
        std::process::exit(1);
    }
    info!("✅ Componenti inizializzati con successo");

    info!("🌟 MATRIX_IDE avviato con successo!");
    info!("📋 Funzionalità disponibili:");
    info!("   • DAG Viewer - Visualizzazione grafi interattiva");
    info!("   • AI Panel - Comunicazione con agenti AI");
    info!("   • Plugin System - Sistema di plugin modulare");
    info!("   • Theme Manager - Gestione temi personalizzati");
    info!("   • God Mode Panel - Funzionalità avanzate");

    // Avvia l'applicazione
    info!("🎯 Avvio interfaccia grafica...");
    if let Err(e) = app.run() {
        error!("❌ Errore durante l'esecuzione: {}", e);
        std::process::exit(1);
    }

    info!("👋 MATRIX_IDE terminato correttamente");
}
