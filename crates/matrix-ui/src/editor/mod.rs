//! <PERSON><PERSON><PERSON> dell\'editor di MATRIX IDE
//!
//! Questo modulo contiene l\'implementazione dell\'editor di MATRIX IDE
//! basato su Lapce e Floem 0.2.

pub mod lapce;

use floem::reactive::SignalGet;

pub use lapce::{
    AdvancedEditor,
    EditorConfig,
    FileInfo,
    EditorViewMode,
    WrapMode,
    // Alias per compatibilità
    LapceEditor,
    LapceConfig
};

/// Trait comune per tutti gli editor in MATRIX IDE
pub trait Editor {
    /// Apre un file nell\'editor
    fn open_file(&mut self, path: &std::path::Path) -> Result<(), crate::error::UiError>;

    /// Salva il contenuto dell\'editor nel file corrente
    fn save(&self) -> Result<(), crate::error::UiError>;

    /// Salva il contenuto dell\'editor in un nuovo file
    fn save_as(&self, path: &std::path::Path) -> Result<(), crate::error::UiError>;

    /// Controlla se l\'editor è stato modificato
    fn is_modified(&self) -> bool;

    /// Ottiene il percorso del file corrente
    fn get_current_file(&self) -> Option<std::path::PathBuf>;

    /// Aggiorna il tema dell\'editor
    fn update_theme(&self, theme: crate::theme::Theme) -> Result<(), crate::error::UiError>;
}

impl Editor for LapceEditor {
    fn open_file(&mut self, path: &std::path::Path) -> Result<(), crate::error::UiError> {
        self.open_file_from_path(path)
    }

    fn save(&self) -> Result<(), crate::error::UiError> {
        self.save_current_file()
    }

    fn save_as(&self, path: &std::path::Path) -> Result<(), crate::error::UiError> {
        // TODO: Implement save_as functionality
        Ok(())
    }

    fn is_modified(&self) -> bool {
        self.modified.get()
    }

    fn get_current_file(&self) -> Option<std::path::PathBuf> {
        self.current_file.get().map(|f| f.path)
    }

    fn update_theme(&self, theme: crate::theme::Theme) -> Result<(), crate::error::UiError> {
        // TODO: Implement theme update
        Ok(())
    }
}
