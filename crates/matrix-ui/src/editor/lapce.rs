//! Editor Panel Avanzato per MATRIX IDE
//!
//! Questo modulo implementa un editor avanzato che integra il meglio di Lapce
//! con Floem 0.2, fornendo syntax highlighting, LSP support e funzionalità moderne.

use crate::error::UiError;
use crate::theme::{Theme, ThemeManager};
use floem::{
    reactive::{create_rw_signal, create_memo, RwSignal, SignalGet, SignalUpdate, SignalWith},
    views::{
        container, h_stack, v_stack, label, empty, scroll, text_editor,
        editor::{
            text::{Document, WrapMethod},
            view::EditorVie<PERSON>,
            Editor, EditorStyle,
        },
        Decorators,
    },
    style::{CursorStyle, Position, Display},
    View, IntoView,
    keyboard::{Key, Modifiers},
    event::{Event, EventListener, EventPropagation},
};
use std::sync::{Arc, RwLock};
use std::path::{Path, PathBuf};
use matrix_core::Engine as CoreEngine;

/// Configurazione dell'editor avanzato
#[derive(Debug, Clone)]
pub struct EditorConfig {
    /// Mostra numeri di riga
    pub show_line_numbers: bool,
    /// Mostra spazi vuoti
    pub show_whitespace: bool,
    /// Dimensione font
    pub font_size: f32,
    /// Famiglia font
    pub font_family: Vec<String>,
    /// Modalità di wrapping del testo
    pub wrap_mode: WrapMode,
    /// Usa tabulazioni (altrimenti spazi)
    pub use_tab: bool,
    /// Dimensione delle tabulazioni
    pub tab_size: usize,
    /// Evidenzia la riga corrente
    pub highlight_current_line: bool,
    /// Abilita syntax highlighting
    pub syntax_highlighting: bool,
    /// Abilita LSP
    pub enable_lsp: bool,
    /// Abilita completamento automatico
    pub auto_completion: bool,
    /// Abilita folding del codice
    pub code_folding: bool,
}

/// Modalità di wrapping del testo
#[derive(Debug, Clone, Copy)]
pub enum WrapMode {
    /// Nessun wrapping
    None,
    /// Wrapping per parola
    Word,
    /// Wrapping per carattere
    Char,
}

impl Default for EditorConfig {
    fn default() -> Self {
        Self {
            show_line_numbers: true,
            show_whitespace: false,
            font_size: 14.0,
            font_family: vec!["Fira Code".to_string(), "JetBrains Mono".to_string(), "Consolas".to_string()],
            wrap_mode: WrapMode::None,
            use_tab: false,
            tab_size: 4,
            highlight_current_line: true,
            syntax_highlighting: true,
            enable_lsp: true,
            auto_completion: true,
            code_folding: true,
        }
    }
}

/// Informazioni sul file aperto
#[derive(Debug, Clone)]
pub struct FileInfo {
    /// Path del file
    pub path: PathBuf,
    /// Nome del file
    pub name: String,
    /// Linguaggio di programmazione
    pub language: String,
    /// Contenuto del file
    pub content: String,
    /// Flag di modifica
    pub modified: bool,
    /// Posizione del cursore
    pub cursor_position: (usize, usize), // (line, column)
}

impl FileInfo {
    pub fn new(path: PathBuf) -> Self {
        let name = path.file_name()
            .and_then(|n| n.to_str())
            .unwrap_or("Untitled")
            .to_string();

        let language = Self::detect_language(&path);

        Self {
            path,
            name,
            language,
            content: String::new(),
            modified: false,
            cursor_position: (0, 0),
        }
    }

    fn detect_language(path: &Path) -> String {
        match path.extension().and_then(|ext| ext.to_str()) {
            Some("rs") => "rust".to_string(),
            Some("js") | Some("jsx") => "javascript".to_string(),
            Some("ts") | Some("tsx") => "typescript".to_string(),
            Some("py") => "python".to_string(),
            Some("go") => "go".to_string(),
            Some("cpp") | Some("cc") | Some("cxx") => "cpp".to_string(),
            Some("c") => "c".to_string(),
            Some("java") => "java".to_string(),
            Some("md") => "markdown".to_string(),
            Some("json") => "json".to_string(),
            Some("toml") => "toml".to_string(),
            Some("yaml") | Some("yml") => "yaml".to_string(),
            _ => "text".to_string(),
        }
    }
}

/// Editor Panel Avanzato per MATRIX IDE
pub struct AdvancedEditor {
    /// Configurazione dell'editor
    config: Arc<RwLock<EditorConfig>>,
    /// Core engine di MATRIX
    core: Arc<CoreEngine>,
    /// Gestore dei temi
    theme_manager: Arc<ThemeManager>,
    /// File attualmente aperto
    current_file: RwSignal<Option<FileInfo>>,
    /// Contenuto dell'editor
    content: RwSignal<String>,
    /// Flag di modifica
    modified: RwSignal<bool>,
    /// Posizione del cursore
    cursor_position: RwSignal<(usize, usize)>,
    /// Lista dei file aperti (tabs)
    open_files: RwSignal<Vec<FileInfo>>,
    /// Indice del file attivo
    active_file_index: RwSignal<usize>,
    /// Flag per mostrare/nascondere la gutter
    show_gutter: RwSignal<bool>,
    /// Modalità di visualizzazione
    view_mode: RwSignal<EditorViewMode>,
}

/// Modalità di visualizzazione dell'editor
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum EditorViewMode {
    /// Modalità normale
    Normal,
    /// Modalità split orizzontale
    SplitHorizontal,
    /// Modalità split verticale
    SplitVertical,
    /// Modalità diff
    Diff,
}

impl AdvancedEditor {
    /// Crea un nuovo editor avanzato
    pub fn new(
        core: Arc<CoreEngine>,
        theme_manager: Arc<ThemeManager>,
    ) -> Result<Self, UiError> {
        let config = Arc::new(RwLock::new(EditorConfig::default()));

        Ok(Self {
            config,
            core,
            theme_manager,
            current_file: create_rw_signal(None),
            content: create_rw_signal(String::new()),
            modified: create_rw_signal(false),
            cursor_position: create_rw_signal((0, 0)),
            open_files: create_rw_signal(Vec::new()),
            active_file_index: create_rw_signal(0),
            show_gutter: create_rw_signal(true),
            view_mode: create_rw_signal(EditorViewMode::Normal),
        })
    }

    /// Apre un file nell'editor
    pub fn open_file(&self, path: PathBuf) -> Result<(), UiError> {
        // Legge il contenuto del file
        let content = std::fs::read_to_string(&path)
            .map_err(|e| UiError::IoError(format!("Failed to read file: {}", e)))?;

        let file_info = FileInfo {
            path: path.clone(),
            name: path.file_name()
                .and_then(|n| n.to_str())
                .unwrap_or("Untitled")
                .to_string(),
            language: FileInfo::detect_language(&path),
            content: content.clone(),
            modified: false,
            cursor_position: (0, 0),
        };

        // Aggiunge il file alla lista dei file aperti
        self.open_files.update(|files| {
            // Controlla se il file è già aperto
            if let Some(index) = files.iter().position(|f| f.path == path) {
                // File già aperto, lo rende attivo
                self.active_file_index.set(index);
            } else {
                // Nuovo file, lo aggiunge
                files.push(file_info.clone());
                self.active_file_index.set(files.len() - 1);
            }
        });

        // Aggiorna il contenuto e il file corrente
        self.content.set(content);
        self.current_file.set(Some(file_info));
        self.modified.set(false);

        Ok(())
    }

    /// Salva il file corrente
    pub fn save_current_file(&self) -> Result<(), UiError> {
        if let Some(file_info) = self.current_file.get() {
            let content = self.content.get();
            std::fs::write(&file_info.path, content)
                .map_err(|e| UiError::IoError(format!("Failed to save file: {}", e)))?;

            self.modified.set(false);

            // Aggiorna il file nella lista
            self.open_files.update(|files| {
                let active_index = self.active_file_index.get();
                if let Some(file) = files.get_mut(active_index) {
                    file.content = self.content.get();
                    file.modified = false;
                }
            });
        }
        Ok(())
    }

    /// Chiude il file corrente
    pub fn close_current_file(&self) -> Result<(), UiError> {
        let active_index = self.active_file_index.get();

        self.open_files.update(|files| {
            if !files.is_empty() && active_index < files.len() {
                files.remove(active_index);

                if files.is_empty() {
                    // Nessun file aperto
                    self.current_file.set(None);
                    self.content.set(String::new());
                    self.active_file_index.set(0);
                } else {
                    // Seleziona il file precedente o il primo
                    let new_index = if active_index > 0 { active_index - 1 } else { 0 };
                    self.active_file_index.set(new_index);

                    if let Some(file) = files.get(new_index) {
                        self.current_file.set(Some(file.clone()));
                        self.content.set(file.content.clone());
                    }
                }
            }
        });

        self.modified.set(false);
        Ok(())
    }

    /// Cambia il file attivo
    pub fn switch_to_file(&self, index: usize) -> Result<(), UiError> {
        self.open_files.with(|files| {
            if let Some(file) = files.get(index) {
                self.active_file_index.set(index);
                self.current_file.set(Some(file.clone()));
                self.content.set(file.content.clone());
                self.modified.set(file.modified);
                Ok(())
            } else {
                Err(UiError::EditorError("Invalid file index".to_string()))
            }
        })
    }

    /// Crea la vista dell'editor
    pub fn create_view(&self) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let content = self.content;
        let current_file = self.current_file;
        let modified = self.modified;
        let open_files = self.open_files;
        let active_file_index = self.active_file_index;
        let show_gutter = self.show_gutter;

        v_stack((
            // Tab bar per i file aperti
            self.create_tab_bar(),

            // Editor principale
            self.create_editor_area(),

            // Status bar dell'editor
            self.create_status_bar(),
        ))
        .style(move |s| {
            s.size_full()
                .background(theme.colors.background)
        })
    }

    /// Crea la barra dei tab
    fn create_tab_bar(&self) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let open_files = self.open_files;
        let active_file_index = self.active_file_index;

        container(
            scroll(
                container(
                    label(move || {
                        let files = open_files.get();
                        let active_idx = active_file_index.get();

                        if files.is_empty() {
                            "No files open".to_string()
                        } else if let Some(file) = files.get(active_idx) {
                            format!("{}{}",
                                file.name,
                                if file.modified { " •" } else { "" }
                            )
                        } else {
                            "No active file".to_string()
                        }
                    })
                    .style(move |s| {
                        s.font_size(12.0)
                            .color(theme.colors.text)
                            .padding_horiz(12.0)
                            .padding_vert(8.0)
                    })
                )
            )
        )
        .style(move |s| {
            s.width_full()
                .height(40.0)
                .background(theme.colors.surface)
                .border_bottom(1.0)
                .border_color(theme.colors.border)
        })
    }

    /// Chiude il file all'indice specificato
    fn close_file_at_index(&self, index: usize) -> Result<(), UiError> {
        self.open_files.update(|files| {
            if index < files.len() {
                files.remove(index);

                let active_index = self.active_file_index.get();

                if files.is_empty() {
                    // Nessun file aperto
                    self.current_file.set(None);
                    self.content.set(String::new());
                    self.active_file_index.set(0);
                } else if active_index == index {
                    // Il file attivo è stato chiuso
                    let new_index = if index > 0 { index - 1 } else { 0 };
                    self.active_file_index.set(new_index);

                    if let Some(file) = files.get(new_index) {
                        self.current_file.set(Some(file.clone()));
                        self.content.set(file.content.clone());
                    }
                } else if active_index > index {
                    // Aggiusta l'indice attivo
                    self.active_file_index.set(active_index - 1);
                }
            }
        });

        Ok(())
    }

    /// Crea l'area principale dell'editor
    fn create_editor_area(&self) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let content = self.content;
        let show_gutter = self.show_gutter;
        let current_file = self.current_file;

        container(
            h_stack((
                // Gutter (numeri di riga, breakpoints, etc.)
                container(
                    scroll(
                        container(
                            label(move || {
                                let content_str = content.get();
                                let line_count = content_str.lines().count().max(1);
                                (1..=line_count).map(|i| format!("{:4}", i)).collect::<Vec<_>>().join("\n")
                            })
                            .style(move |s| {
                                s.font_size(12.0)
                                    .color(theme.colors.text_secondary)
                                    .font_family("Fira Code".to_string())
                                    .padding_horiz(8.0)
                                    .line_height(18.0)
                            })
                        )
                    )
                )
                .style(move |s| {
                    s.width(60.0)
                        .height_full()
                        .background(theme.colors.surface)
                        .border_right(1.0)
                        .border_color(theme.colors.border)
                        .display(if show_gutter.get() {
                            Display::Flex
                        } else {
                            Display::None
                        })
                }),

                // Area dell'editor principale
                container(
                    scroll(
                        text_editor(content.get())
                            .style(move |s| {
                                s.font_size(14.0)
                                    .font_family("Fira Code".to_string())
                                    .color(theme.colors.text)
                                    .background(theme.colors.background)
                                    .padding(12.0)
                                    .size_full()
                            })
                            .on_event_stop(EventListener::KeyDown, {
                                let modified = self.modified;
                                move |event| {
                                    if let Event::KeyDown(key_event) = event {
                                        // Marca come modificato quando si digita
                                        if !key_event.modifiers.control() && !key_event.modifiers.alt() {
                                            modified.set(true);
                                        }
                                    }
                                }
                            })
                    )
                )
                .style(|s| s.flex_grow(1.0).height_full()),
            ))
        )
        .style(|s| s.size_full())
    }

    /// Crea la status bar dell'editor
    fn create_status_bar(&self) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let current_file = self.current_file;
        let cursor_position = self.cursor_position;
        let modified = self.modified;

        container(
            h_stack((
                // Informazioni sul file
                label(move || {
                    if let Some(file) = current_file.get() {
                        format!("{}{}",
                            file.name,
                            if modified.get() { " •" } else { "" }
                        )
                    } else {
                        "No file open".to_string()
                    }
                })
                .style(move |s| {
                    s.font_size(12.0)
                        .color(theme.colors.text)
                        .padding_horiz(12.0)
                }),

                // Spacer
                empty().style(|s| s.flex_grow(1.0)),

                // Linguaggio
                label(move || {
                    current_file.get()
                        .map(|f| f.language.clone())
                        .unwrap_or_else(|| "Plain Text".to_string())
                })
                .style(move |s| {
                    s.font_size(12.0)
                        .color(theme.colors.text_secondary)
                        .padding_horiz(12.0)
                }),

                // Posizione del cursore
                label(move || {
                    let (line, col) = cursor_position.get();
                    format!("Ln {}, Col {}", line + 1, col + 1)
                })
                .style(move |s| {
                    s.font_size(12.0)
                        .color(theme.colors.text_secondary)
                        .padding_horiz(12.0)
                }),
            ))
        )
        .style(move |s| {
            s.width_full()
                .height(24.0)
                .background(theme.colors.surface)
                .border_top(1.0)
                .border_color(theme.colors.border)
        })
    }
}

impl Clone for AdvancedEditor {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            core: self.core.clone(),
            theme_manager: self.theme_manager.clone(),
            current_file: self.current_file,
            content: self.content,
            modified: self.modified,
            cursor_position: self.cursor_position,
            open_files: self.open_files,
            active_file_index: self.active_file_index,
            show_gutter: self.show_gutter,
            view_mode: self.view_mode,
        }
    }
}

// Re-export dei tipi principali per compatibilità
pub use EditorConfig as LapceConfig;
pub use AdvancedEditor as LapceEditor;
