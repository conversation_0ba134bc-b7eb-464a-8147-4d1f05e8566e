//! Integrazione dell\'editor <PERSON><PERSON><PERSON> con MATRIX IDE
//!
//! Questo modulo implementa l\'adattatore per integrare Lapce con Floem 0.2
//! e il sistema di temi di MATRIX IDE.

use crate::error::UiError;
use crate::theme::Theme;
use crate::floem_bridge::{F<PERSON>emBridge, UiComponent};
use floem::views::{editor::{text::{Document, SimpleStylingBuilder, WrapMethod}, core::{editor::EditType, selection::Selection}}, text_editor, stack, Decorators};
use floem::reactive::{create_rw_signal, RwSignal, SignalGet, SignalUpdate};
use floem::{IntoView, View};
use std::sync::{Arc, RwLock};
use std::path::{Path, PathBuf};

/// Configurazione dell\'editor Lapce
#[derive(Debug, Clone)]
pub struct LapceConfig {
    /// Mostra numeri di riga
    pub show_line_numbers: bool,

    /// Mostra spazi vuoti
    pub show_whitespace: bool,

    /// Dimensione font
    pub font_size: f32,

    /// Famiglia font
    pub font_family: Vec<String>,

    /// Modalità di wrapping del testo
    pub wrap_mode: WrapMode,

    /// Usa tabulazioni (altrimenti spazi)
    pub use_tab: bool,

    /// Dimensione delle tabulazioni
    pub tab_size: usize,

    /// Mostra la barra di avanzamento
    pub show_progress: bool,

    /// Evidenzia la riga corrente
    pub highlight_current_line: bool,
}

/// Modalità di wrapping del testo
#[derive(Debug, Clone, Copy)]
pub enum WrapMode {
    /// Nessun wrapping
    None,

    /// Wrapping per parola
    Word,

    /// Wrapping per carattere
    Char,
}

impl Default for LapceConfig {
    fn default() -> Self {
        Self {
            show_line_numbers: true,
            show_whitespace: false,
            font_size: 14.0,
            font_family: vec!["Fira Code".to_string(), "Consolas".to_string(), "Monospace".to_string()],
            wrap_mode: WrapMode::None,
            use_tab: false,
            tab_size: 4,
            show_progress: true,
            highlight_current_line: true,
        }
    }
}

/// Integrazione dell\'editor Lapce
pub struct LapceEditor {
    /// Configurazione dell\'editor
    config: Arc<RwLock<LapceConfig>>,

    /// Bridge per l'integrazione con Floem
    floem_bridge: Arc<FloemBridge>,

    /// Path del file attualmente aperto
    current_file: RwSignal<Option<PathBuf>>,

    /// Tema corrente
    theme: Arc<RwLock<Theme>>,

    /// Flag che indica se l'editor è stato modificato
    modified: RwSignal<bool>,

    /// Documento attualmente aperto nell'editor
    current_doc: Option<Arc<dyn Document>>,

    /// Flag che indica se la gutter è visibile
    hide_gutter: RwSignal<bool>,
}

impl LapceEditor {
    /// Crea una nuova istanza dell\'editor Lapce
    pub fn new(theme: Theme, config: Option<LapceConfig>) -> Result<Self, UiError> {
        let config = config.unwrap_or_default();
        let floem_bridge = Arc::new(FloemBridge::new(theme.clone()));
        let current_file = create_rw_signal(None);
        let modified = create_rw_signal(false);
        let hide_gutter = create_rw_signal(!config.show_line_numbers);

        Ok(Self {
            config: Arc::new(RwLock::new(config)),
            floem_bridge,
            current_file,
            theme: Arc::new(RwLock::new(theme)),
            modified,
            current_doc: None,
            hide_gutter,
        })
    }

    /// Crea la vista dell\'editor con il contenuto specificato
    pub fn create_editor_view(&mut self, content: &str) -> Result<impl IntoView, UiError> {
        // Ottieni stili dal bridge
        let editor_style_fn = self.floem_bridge.create_editor_style_fn()?;
        let syntax_builder = self.floem_bridge.create_syntax_highlighting()?;

        // Configura il styling in base alla configurazione
        let config = self.config.read().map_err(|_| {
            UiError::LockError("Failed to acquire read lock on config".to_string())
        })?;

        let wrap_method = match config.wrap_mode {
            WrapMode::None => WrapMethod::None,
            WrapMode::Word => WrapMethod::EditorWidth, // Word wrapping → EditorWidth
            WrapMode::Char => WrapMethod::EditorWidth, // Char wrapping → EditorWidth
        };

        let font_family = config.font_family.iter().map(|name| {
            floem::text::FamilyOwned::Name(name.to_string())
        }).collect::<Vec<_>>();

        // Crea editor con configurazione
        let styling = syntax_builder
            .wrap(wrap_method)
            .font_size(config.font_size as usize)
            .font_family(font_family)
            .tab_width(config.tab_size)
            .build();

        let editor = text_editor(content)
            .styling(styling)
            .editor_style(editor_style_fn)
            .editor_style(move |s| s.hide_gutter(self.hide_gutter.get()))
            .style(|s| s.size_full());

        // Salva il documento per riferimenti futuri
        self.current_doc = Some(editor.doc().clone());

        // Imposta una callback per il cambio di contenuto
        let modified_signal = self.modified;
        let editor = editor.update(move |_| {
            // Quando l'editor cambia, imposta il flag modified a true
            modified_signal.set(true);
        });

        // Crea la vista finale dell'editor
        let editor_view = stack((editor,))
            .style(|s| s.size_full().flex_col());

        Ok(editor_view)
    }

    /// Crea editor con contenuto da file
    pub fn create_editor_from_file(&mut self, path: &Path) -> Result<impl IntoView, UiError> {
        // Leggi il contenuto dal file
        let content = std::fs::read_to_string(path)
            .map_err(|e| UiError::IoError(format!("Failed to read file {}: {}", path.display(), e)))?;

        // Imposta il file corrente
        self.current_file.set(Some(path.to_path_buf()));

        // Resetta il flag modified
        self.modified.set(false);

        // Crea l'editor
        self.create_editor_view(&content)
    }

    /// Salva il contenuto dell'editor sul file corrente
    pub fn save_current_file(&self) -> Result<(), UiError> {
        if let Some(doc) = &self.current_doc {
            if let Some(path) = self.current_file.get() {
                let content = doc.text().to_string();
                std::fs::write(&path, content)
                    .map_err(|e| UiError::IoError(format!("Failed to write to file {}: {}", path.display(), e)))?;

                // Resetta il flag modified
                self.modified.set(false);

                return Ok(());
            }
        }

        Err(UiError::EditorError("No file is currently open".to_string()))
    }

    /// Salva il contenuto dell'editor su un nuovo file
    pub fn save_as(&self, path: &Path) -> Result<(), UiError> {
        if let Some(doc) = &self.current_doc {
            let content = doc.text().to_string();
            std::fs::write(path, content)
                .map_err(|e| UiError::IoError(format!("Failed to write to file {}: {}", path.display(), e)))?;

            // Aggiorna il file corrente
            self.current_file.set(Some(path.to_path_buf()));

            // Resetta il flag modified
            self.modified.set(false);

            return Ok(());
        }

        Err(UiError::EditorError("No document is currently open".to_string()))
    }

    /// Aggiorna la configurazione dell\'editor
    pub fn update_config(&self, config: LapceConfig) -> Result<(), UiError> {
        let mut config_guard = self.config.write().map_err(|_| {
            UiError::LockError("Failed to acquire write lock on config".to_string())
        })?;

        *config_guard = config.clone();
        drop(config_guard);

        // Aggiorna flag gutter
        self.hide_gutter.set(!config.show_line_numbers);

        Ok(())
    }

    /// Aggiorna il tema dell\'editor
    pub fn update_theme(&self, theme: Theme) -> Result<(), UiError> {
        let mut theme_guard = self.theme.write().map_err(|_| {
            UiError::LockError("Failed to acquire write lock on theme".to_string())
        })?;

        *theme_guard = theme.clone();
        drop(theme_guard);

        // Aggiorna il bridge con il nuovo tema
        self.floem_bridge.update_theme(theme)?;

        Ok(())
    }

    /// Controlla se l'editor è stato modificato
    pub fn is_modified(&self) -> bool {
        self.modified.get()
    }

    /// Ottiene il percorso del file corrente
    pub fn get_current_file(&self) -> Option<PathBuf> {
        self.current_file.get()
    }

    /// Imposta la visibilità dei numeri di riga
    pub fn set_line_numbers_visibility(&self, show: bool) -> Result<(), UiError> {
        let mut config_guard = self.config.write().map_err(|_| {
            UiError::LockError("Failed to acquire write lock on config".to_string())
        })?;

        config_guard.show_line_numbers = show;
        drop(config_guard);

        // Aggiorna flag gutter
        self.hide_gutter.set(!show);

        Ok(())
    }

    /// Pulisce il contenuto dell\'editor
    pub fn clear_content(&self) -> Result<(), UiError> {
        if let Some(doc) = &self.current_doc {
            doc.edit_single(
                Selection::region(0, doc.text().len()),
                "",
                EditType::DeleteSelection,
            );

            // Imposta il flag modified
            self.modified.set(true);

            Ok(())
        } else {
            Err(UiError::EditorError("No document is currently open".to_string()))
        }
    }

    /// Salva il file corrente (placeholder per compatibilità)
    pub fn save(&mut self) -> Result<(), UiError> {
        // TODO: Implement save functionality
        self.modified.set(false);
        Ok(())
    }

    /// Apre un file (placeholder per compatibilità)
    pub fn open_file(&mut self, _path: PathBuf) -> Result<(), UiError> {
        // TODO: Implement file opening functionality
        Ok(())
    }

    // Metodo duplicato rimosso - mantenuto quello originale sopra
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::theme::Theme;

    #[test]
    fn test_lapce_editor_creation() {
        let theme = Theme::default();
        let editor = LapceEditor::new(theme, None);
        assert!(editor.is_ok());
    }
}
