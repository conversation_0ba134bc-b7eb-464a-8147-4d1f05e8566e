//! Modulo principale dell'applicazione UI
//!
//! Questo modulo gestisce l'applicazione UI principale, coordinando
//! tutti i componenti dell'interfaccia utente.

use std::sync::{Arc, RwLock};
use std::path::PathBuf;
use floem::View;
use matrix_core::{Engine as CoreEngine, EventBus, GlobalState, EventSubscriber, EventHandler, Event};
use crate::{
    error::UiError,
    theme::ThemeManager,
    layout::MainLayout,
    panels::PanelManager,
    lapce_bridge::LapceIntegration,
    plugin_host::PluginHost,
    plugin_integrator::{PluginHostIntegrator, PluginIntegrationError},
};
use async_trait::async_trait;

/// Applicazione UI principale
pub struct App {
    /// Riferimento al Core Engine
    core: Arc<CoreEngine>,

    /// Gestore dei temi
    theme_manager: Arc<ThemeManager>,

    /// Gestore dei pannelli
    panel_manager: Arc<PanelManager>,

    /// Integrazione con Lapce
    lapce: Arc<LapceIntegration>,

    /// Gestore dei plugin UI
    plugin_host: Arc<PluginHost>,

    /// Integratore plugin-host
    plugin_integrator: Option<PluginHostIntegrator>,

    /// Layout principale dell'applicazione
    layout: Arc<RwLock<MainLayout>>,

    /// ID di sottoscrizione agli eventi
    subscription_id: Option<uuid::Uuid>,

    /// Configurazione headless (per test)
    headless: bool,
}

impl App {
    /// Crea una nuova istanza dell'applicazione UI
    pub fn new() -> Result<Self, UiError> {
        // Inizializza il Core Engine
        let core = Arc::new(matrix_core::init()?);

        // Inizializza il gestore dei temi
        let theme_manager = Arc::new(ThemeManager::new()?);

        // Inizializza l'integrazione con Lapce
        let lapce = Arc::new(LapceIntegration::new()?);

        // Inizializza il gestore dei pannelli
        let panel_manager = Arc::new(PanelManager::new(
            core.clone(),
            theme_manager.clone(),
            lapce.clone(),
        )?);

        // Inizializza il gestore dei plugin UI
        let plugin_host = Arc::new(PluginHost::new(
            core.clone(),
            panel_manager.clone(),
            theme_manager.clone(),
        ));

        // Inizializza il layout principale
        let layout = Arc::new(RwLock::new(MainLayout::new(
            panel_manager.clone(),
            theme_manager.clone(),
        )?));

        // Crea l'applicazione
        let app = Self {
            core,
            theme_manager,
            panel_manager,
            lapce,
            plugin_host,
            plugin_integrator: None,
            layout,
            subscription_id: None,
            headless: false,
        };

        Ok(app)
    }

    /// Inizializza l'applicazione
    pub fn initialize(&mut self) -> Result<(), UiError> {
        // Avvia il Core Engine
        self.core.start()?;

        // Inizializza i componenti
        self.theme_manager.initialize()?;
        self.lapce.initialize()?;
        self.panel_manager.initialize()?;

        // Inizializza il gestore dei plugin
        self.plugin_host.initialize().map_err(|e| {
            UiError::PluginError(format!("Failed to initialize plugin host: {}", e))
        })?;

        // Inizializza l'integratore plugin-host
        self.initialize_plugin_integrator()?;

        // Sottoscrive agli eventi
        let event_handler = Arc::new(AppEventHandler {
            app: Arc::new(RwLock::new(self.clone())),
        });

        let subscriber = Arc::new(EventSubscriber::new(
            vec!["*"], // L'applicazione riceve tutti gli eventi
            event_handler,
        ));

        let subscription_id = self.core.event_bus().subscribe(subscriber)?;
        self.subscription_id = Some(subscription_id);

        Ok(())
    }

    /// Inizializza l'integratore plugin-host e carica i plugin
    fn initialize_plugin_integrator(&mut self) -> Result<(), UiError> {
        // Ottieni il percorso del registry
        let home_dir = dirs::home_dir().unwrap_or_else(|| PathBuf::from("."));
        let registry_path = home_dir.join(".matrix").join("plugins").join("registry");

        // Crea l'integratore
        let plugin_integrator = PluginHostIntegrator::new(
            self.plugin_host.clone(),
            registry_path,
        ).map_err(|e| {
            UiError::PluginError(format!("Failed to create plugin integrator: {}", e))
        })?;

        self.plugin_integrator = Some(plugin_integrator);

        // Carica tutti i plugin
        if !self.headless {
            if let Some(integrator) = &mut self.plugin_integrator {
                integrator.load_all_plugins().map_err(|e| {
                    UiError::PluginError(format!("Failed to load plugins: {}", e))
                })?;
            }
        }

        Ok(())
    }

    /// Imposta la modalità headless (per test)
    pub fn set_headless(&mut self, headless: bool) {
        self.headless = headless;
    }

    /// Avvia l'applicazione
    pub fn run(&self) -> Result<(), UiError> {
        // Ottiene il layout principale
        let layout = {
            let layout_guard = self.layout.read().map_err(|_| {
                UiError::LockError("Failed to acquire read lock on layout".to_string())
            })?;

            layout_guard.build()?
        };

        // Avvia l'applicazione Floem
        floem::launch(move || layout);

        Ok(())
    }

    /// Arresta l'applicazione
    pub fn shutdown(&self) -> Result<(), UiError> {
        // Rimuove la sottoscrizione agli eventi
        if let Some(subscription_id) = self.subscription_id {
            self.core.event_bus().unsubscribe(subscription_id)?;
        }

        // Arresta i componenti
        self.plugin_host.shutdown().map_err(|e| {
            UiError::PluginError(format!("Failed to shutdown plugin host: {}", e))
        })?;
        self.panel_manager.shutdown()?;
        self.lapce.shutdown()?;
        self.theme_manager.shutdown()?;

        // Arresta il Core Engine
        self.core.stop()?;

        Ok(())
    }

    /// Ottiene un riferimento al Core Engine
    pub fn core(&self) -> Arc<CoreEngine> {
        self.core.clone()
    }

    /// Ottiene un riferimento al gestore dei temi
    pub fn theme_manager(&self) -> Arc<ThemeManager> {
        self.theme_manager.clone()
    }

    /// Ottiene un riferimento al gestore dei pannelli
    pub fn panel_manager(&self) -> Arc<PanelManager> {
        self.panel_manager.clone()
    }

    /// Ottiene un riferimento all'integrazione con Lapce
    pub fn lapce(&self) -> Arc<LapceIntegration> {
        self.lapce.clone()
    }

    /// Ottiene un riferimento al gestore dei plugin
    pub fn plugin_host(&self) -> Arc<PluginHost> {
        self.plugin_host.clone()
    }

    /// Ottiene un riferimento all'integratore plugin-host
    pub fn plugin_integrator(&self) -> Option<&PluginHostIntegrator> {
        self.plugin_integrator.as_ref()
    }

    /// Ottiene un riferimento mutabile all'integratore plugin-host
    pub fn plugin_integrator_mut(&mut self) -> Option<&mut PluginHostIntegrator> {
        self.plugin_integrator.as_mut()
    }
}

impl Clone for App {
    fn clone(&self) -> Self {
        Self {
            core: self.core.clone(),
            theme_manager: self.theme_manager.clone(),
            panel_manager: self.panel_manager.clone(),
            lapce: self.lapce.clone(),
            plugin_host: self.plugin_host.clone(),
            plugin_integrator: None, // Non cloniamo l'integratore
            layout: self.layout.clone(),
            subscription_id: self.subscription_id,
            headless: self.headless,
        }
    }
}

/// Handler degli eventi per l'applicazione
struct AppEventHandler {
    /// Riferimento all'applicazione
    app: Arc<RwLock<App>>,
}

#[async_trait]
impl EventHandler for AppEventHandler {
    async fn handle(&self, event: Event) -> Result<(), matrix_core::CoreError> {
        match event {
            Event::SystemStarted => {
                // Gestisce l'evento di avvio del sistema
                println!("System started event received");
            }
            Event::SystemStopping => {
                // Gestisce l'evento di arresto del sistema
                println!("System stopping event received");
            }
            _ => {
                // Gestisce altri eventi
            }
        }

        Ok(())
    }
}
