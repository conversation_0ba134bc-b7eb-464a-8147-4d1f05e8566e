//! MATRIX_IDE Title Bar Component
//!
//! Implementazione professionale della barra del titolo per MATRIX_IDE.
//! Basata su Lapce e ottimizzata per Floem 0.2 API.
//! 
//! Funzionalità:
//! - Logo MATRIX_IDE
//! - Menu principale (File, Edit, View, etc.)
//! - Controlli finestra (minimize, maximize, close)
//! - Indicatori di stato
//! - Supporto per ultra/god mode features

use std::sync::Arc;
use floem::{
    reactive::{RwSignal, SignalGet, SignalUpdate, create_rw_signal},
    views::{
        container, h_stack, label, empty, stack, v_stack, Decorators
    },
    style::{AlignItems, JustifyContent, CursorStyle, Position, Display},
    text::Weight,
    View, IntoView,
    action::{add_overlay, remove_overlay},
};

use crate::{
    theme::{ThemeManager, Theme},
    error::UiError,
};

/// Elemento di menu dropdown
#[derive(Debug, Clone)]
pub struct MenuItem {
    pub label: String,
    pub action: Option<String>, // Per ora usiamo String, poi implementeremo callback
    pub enabled: bool,
    pub separator_after: bool,
}

impl MenuItem {
    pub fn new(label: &str) -> Self {
        Self {
            label: label.to_string(),
            action: None,
            enabled: true,
            separator_after: false,
        }
    }

    pub fn with_action(mut self, action: &str) -> Self {
        self.action = Some(action.to_string());
        self
    }

    pub fn disabled(mut self) -> Self {
        self.enabled = false;
        self
    }

    pub fn with_separator(mut self) -> Self {
        self.separator_after = true;
        self
    }
}

/// Menu dropdown completo
#[derive(Debug, Clone)]
pub struct DropdownMenu {
    pub title: String,
    pub items: Vec<MenuItem>,
}

impl DropdownMenu {
    pub fn new(title: &str) -> Self {
        Self {
            title: title.to_string(),
            items: Vec::new(),
        }
    }

    pub fn add_item(mut self, item: MenuItem) -> Self {
        self.items.push(item);
        self
    }
}

/// Configurazione della title bar
#[derive(Debug, Clone)]
pub struct TitleBarConfig {
    /// Altezza della title bar
    pub height: f64,
    /// Mostra il logo MATRIX
    pub show_logo: bool,
    /// Mostra i controlli finestra
    pub show_window_controls: bool,
    /// Titolo personalizzato
    pub custom_title: Option<String>,
}

impl Default for TitleBarConfig {
    fn default() -> Self {
        Self {
            height: 37.0, // Stessa altezza di Lapce
            show_logo: true,
            show_window_controls: !cfg!(target_os = "macos"), // Su macOS usa i controlli nativi
            custom_title: None,
        }
    }
}

/// Componente Title Bar per MATRIX_IDE
pub struct TitleBar {
    /// Configurazione
    config: TitleBarConfig,
    /// Gestore temi
    theme_manager: Arc<ThemeManager>,
    /// Stato della finestra (massimizzata o no)
    window_maximized: RwSignal<bool>,
    /// Titolo corrente
    current_title: RwSignal<String>,
}

impl TitleBar {
    /// Crea una nuova title bar
    pub fn new(theme_manager: Arc<ThemeManager>) -> Result<Self, UiError> {
        Ok(Self {
            config: TitleBarConfig::default(),
            theme_manager,
            window_maximized: create_rw_signal(false),
            current_title: create_rw_signal("MATRIX_IDE".to_string()),
        })
    }

    /// Crea una title bar con configurazione personalizzata
    pub fn with_config(
        theme_manager: Arc<ThemeManager>,
        config: TitleBarConfig,
    ) -> Result<Self, UiError> {
        Ok(Self {
            config,
            theme_manager,
            window_maximized: create_rw_signal(false),
            current_title: create_rw_signal("MATRIX_IDE".to_string()),
        })
    }

    /// Imposta il titolo
    pub fn set_title(&self, title: String) {
        self.current_title.set(title);
    }

    /// Ottiene lo stato della finestra
    pub fn is_maximized(&self) -> bool {
        self.window_maximized.get()
    }

    /// Imposta lo stato della finestra
    pub fn set_maximized(&self, maximized: bool) {
        self.window_maximized.set(maximized);
    }



    /// Costruisce la title bar completa
    pub fn build(&self) -> Result<Box<dyn View>, UiError> {
        let theme = self.theme_manager.get_active_theme()?;
        let current_title = self.current_title;
        let window_maximized = self.window_maximized;
        let config_height = self.config.height;
        let show_window_controls = self.config.show_window_controls;

        // Logo MATRIX_IDE
        let logo = container(
            label(|| "⚡".to_string())
                .style(move |s| {
                    s.font_size(18.0)
                        .color(theme.colors.primary)
                })
        )
        .style(move |s| {
            s.size(24.0, 24.0)
                .align_items(Some(AlignItems::Center))
                .justify_content(Some(JustifyContent::Center))
                .border_radius(4.0)
                .background(theme.colors.surface)
                .cursor(CursorStyle::Pointer)
        });

        // Sezione sinistra
        let left_section = h_stack((
            logo,
            empty().style(|s| s.width(16.0)),
            label(move || current_title.get())
                .style(move |s| {
                    s.font_size(14.0)
                        .font_weight(Weight::BOLD)
                        .color(theme.colors.text)
                }),
        ))
        .style(|s| {
            s.align_items(Some(AlignItems::Center))
                .padding_left(12.0)
        });

        // Menu centrale con dropdown
        let matrix_menus = create_matrix_menus();

        let center_section = h_stack((
            matrix_menus.into_iter().map(|menu| {
                create_dropdown_menu(menu, &theme)
            }).collect::<Vec<_>>(),
        ))
        .style(|s| {
            s.align_items(Some(AlignItems::Center))
                .gap(4.0)
        });

        // Controlli finestra (se abilitati)
        let right_section: Box<dyn View> = if show_window_controls {
            let minimize_btn = "−".to_string()
                .on_click_stop(|_| {
                    println!("Minimize window");
                })
                .style(move |s| {
                    s.size(32.0, 24.0)
                        .font_size(14.0)
                        .color(theme.colors.text)
                        .background(theme.colors.transparent)
                        .border_radius(0.0)
                        .cursor(CursorStyle::Pointer)
                        .hover(|s| s.background(theme.colors.hover))
                        .active(|s| s.background(theme.colors.active))
                        .align_items(Some(AlignItems::Center))
                        .justify_content(Some(JustifyContent::Center))
                });

            let maximize_btn = label(move || {
                if window_maximized.get() { "❐" } else { "□" }.to_string()
            })
            .on_click_stop(move |_| {
                let new_state = !window_maximized.get();
                window_maximized.set(new_state);
                println!("Toggle maximize: {}", new_state);
            })
            .style(move |s| {
                s.size(32.0, 24.0)
                    .font_size(12.0)
                    .color(theme.colors.text)
                    .background(theme.colors.transparent)
                    .border_radius(0.0)
                    .cursor(CursorStyle::Pointer)
                    .hover(|s| s.background(theme.colors.hover))
                    .active(|s| s.background(theme.colors.active))
                    .align_items(Some(AlignItems::Center))
                    .justify_content(Some(JustifyContent::Center))
            });

            let close_btn = "×".to_string()
                .on_click_stop(|_| {
                    println!("Close window");
                    std::process::exit(0);
                })
                .style(move |s| {
                    s.size(32.0, 24.0)
                        .font_size(16.0)
                        .color(theme.colors.text)
                        .background(theme.colors.transparent)
                        .border_radius(0.0)
                        .cursor(CursorStyle::Pointer)
                        .hover(|s| s.background(theme.colors.danger))
                        .active(|s| s.background(theme.colors.danger_active))
                        .align_items(Some(AlignItems::Center))
                        .justify_content(Some(JustifyContent::Center))
                });

            Box::new(h_stack((minimize_btn, maximize_btn, close_btn))
                .style(|s| s.align_items(Some(AlignItems::Center))))
        } else {
            Box::new(empty())
        };

        let title_bar = h_stack((
            left_section,
            empty().style(|s| s.flex_grow(1.0)),
            center_section,
            empty().style(|s| s.flex_grow(1.0)),
            right_section,
        ))
        .style(move |s| {
            s.width_pct(100.0)
                .height(config_height)
                .align_items(Some(AlignItems::Center))
                .background(theme.colors.surface)
                .border_bottom(1.0)
                .border_color(theme.colors.border)
        });

        Ok(Box::new(title_bar))
    }
}

/// Funzione helper per creare una title bar con configurazione di default
pub fn matrix_title_bar(theme_manager: Arc<ThemeManager>) -> Result<Box<dyn View>, UiError> {
    let title_bar = TitleBar::new(theme_manager)?;
    title_bar.build()
}

/// Funzione helper per creare una title bar con configurazione personalizzata
pub fn matrix_title_bar_with_config(
    theme_manager: Arc<ThemeManager>,
    config: TitleBarConfig,
) -> Result<Box<dyn View>, UiError> {
    let title_bar = TitleBar::with_config(theme_manager, config)?;
    title_bar.build()
}

/// Crea un singolo dropdown menu
fn create_dropdown_menu(menu: DropdownMenu, theme: &Theme) -> Box<dyn View> {
    let is_open = create_rw_signal(false);
    let menu_title = menu.title.clone();
    let menu_items = menu.items.clone();

    // Cloniamo i colori del tema per evitare problemi di lifetime
    let text_color = theme.colors.text;
    let transparent_color = theme.colors.transparent;
    let hover_color = theme.colors.hover;
    let active_color = theme.colors.active;
    let background_color = theme.colors.background;
    let border_color = theme.colors.border;

    // Pulsante principale del menu
    let menu_button = menu_title.clone()
        .style(move |s| {
            s.padding_horiz(12.0)
                .padding_vert(6.0)
                .font_size(13.0)
                .color(text_color)
                .background(transparent_color)
                .border_radius(4.0)
                .cursor(CursorStyle::Pointer)
                .hover(|s| s.background(hover_color))
                .active(|s| s.background(active_color))
        })
        .on_click_stop(move |_| {
            is_open.update(|open| *open = !*open);
            println!("Menu clicked: {} - Open: {}", menu_title, !is_open.get());
        });

    // Dropdown menu items - creiamo elementi fissi per testare
    let first_item = if !menu_items.is_empty() {
        let item = &menu_items[0];
        let item_label = item.label.clone();
        let item_action = item.action.clone();

        container(
            label(move || item_label.clone())
                .style(move |s| {
                    s.padding_horiz(16.0)
                        .padding_vert(8.0)
                        .font_size(12.0)
                        .color(text_color)
                        .cursor(CursorStyle::Pointer)
                        .hover(|s| s.background(hover_color))
                })
                .on_click_stop(move |_| {
                    if let Some(action) = &item_action {
                        println!("Menu action: {}", action);
                    }
                    is_open.set(false);
                })
        )
        .style(move |s| {
            s.width_full()
        })
    } else {
        container(label(|| "No items".to_string()))
    };

    let second_item = if menu_items.len() > 1 {
        let item = &menu_items[1];
        let item_label = item.label.clone();
        let item_action = item.action.clone();

        container(
            label(move || item_label.clone())
                .style(move |s| {
                    s.padding_horiz(16.0)
                        .padding_vert(8.0)
                        .font_size(12.0)
                        .color(text_color)
                        .cursor(CursorStyle::Pointer)
                        .hover(|s| s.background(hover_color))
                })
                .on_click_stop(move |_| {
                    if let Some(action) = &item_action {
                        println!("Menu action: {}", action);
                    }
                    is_open.set(false);
                })
        )
        .style(move |s| {
            s.width_full()
        })
    } else {
        container(empty())
    };

    // Container per il dropdown con layout verticale
    let dropdown_items = container(
        v_stack((first_item, second_item))
    )
    .style(move |s| {
        s.background(background_color)
            .border(1.0)
            .border_color(border_color)
            .border_radius(4.0)
            .min_width(150.0)
            .position(Position::Absolute)
            .inset_top_pct(100.0)
            .inset_left(0.0)
            .z_index(1000)
            .display(if is_open.get() { floem::style::Display::Flex } else { floem::style::Display::None })
    });

    // Container principale con posizionamento relativo per il dropdown assoluto
    let menu_container = container(menu_button)
        .style(|s| {
            s.position(Position::Relative)
        });

    // Stack che contiene il container del menu e il dropdown come overlay
    let menu_with_dropdown = stack((
        menu_container,
        dropdown_items,
    ));

    Box::new(menu_with_dropdown)
}

/// Crea i menu dropdown standard per MATRIX_IDE
fn create_matrix_menus() -> Vec<DropdownMenu> {
    vec![
        // File Menu
        DropdownMenu::new("File")
            .add_item(MenuItem::new("New File").with_action("file.new"))
            .add_item(MenuItem::new("New Window").with_action("file.new_window"))
            .add_item(MenuItem::new("Open File...").with_action("file.open").with_separator())
            .add_item(MenuItem::new("Open Folder...").with_action("file.open_folder"))
            .add_item(MenuItem::new("Open Recent").with_action("file.recent").with_separator())
            .add_item(MenuItem::new("Save").with_action("file.save"))
            .add_item(MenuItem::new("Save As...").with_action("file.save_as"))
            .add_item(MenuItem::new("Save All").with_action("file.save_all").with_separator())
            .add_item(MenuItem::new("Close").with_action("file.close"))
            .add_item(MenuItem::new("Exit").with_action("file.exit")),

        // Edit Menu
        DropdownMenu::new("Edit")
            .add_item(MenuItem::new("Undo").with_action("edit.undo"))
            .add_item(MenuItem::new("Redo").with_action("edit.redo").with_separator())
            .add_item(MenuItem::new("Cut").with_action("edit.cut"))
            .add_item(MenuItem::new("Copy").with_action("edit.copy"))
            .add_item(MenuItem::new("Paste").with_action("edit.paste").with_separator())
            .add_item(MenuItem::new("Find").with_action("edit.find"))
            .add_item(MenuItem::new("Replace").with_action("edit.replace"))
            .add_item(MenuItem::new("Find in Files").with_action("edit.find_in_files")),

        // View Menu
        DropdownMenu::new("View")
            .add_item(MenuItem::new("Command Palette").with_action("view.command_palette"))
            .add_item(MenuItem::new("File Explorer").with_action("view.explorer").with_separator())
            .add_item(MenuItem::new("DAG Viewer").with_action("view.dag"))
            .add_item(MenuItem::new("AI Panel").with_action("view.ai_panel"))
            .add_item(MenuItem::new("God Mode").with_action("view.god_mode").with_separator())
            .add_item(MenuItem::new("Terminal").with_action("view.terminal"))
            .add_item(MenuItem::new("Problems").with_action("view.problems")),

        // Selection Menu
        DropdownMenu::new("Selection")
            .add_item(MenuItem::new("Select All").with_action("selection.all"))
            .add_item(MenuItem::new("Select Line").with_action("selection.line"))
            .add_item(MenuItem::new("Select Word").with_action("selection.word").with_separator())
            .add_item(MenuItem::new("Expand Selection").with_action("selection.expand"))
            .add_item(MenuItem::new("Shrink Selection").with_action("selection.shrink")),

        // Go Menu
        DropdownMenu::new("Go")
            .add_item(MenuItem::new("Go to File").with_action("go.file"))
            .add_item(MenuItem::new("Go to Symbol").with_action("go.symbol"))
            .add_item(MenuItem::new("Go to Line").with_action("go.line").with_separator())
            .add_item(MenuItem::new("Go Back").with_action("go.back"))
            .add_item(MenuItem::new("Go Forward").with_action("go.forward")),

        // Run Menu
        DropdownMenu::new("Run")
            .add_item(MenuItem::new("Start Debugging").with_action("run.debug"))
            .add_item(MenuItem::new("Run Without Debugging").with_action("run.start").with_separator())
            .add_item(MenuItem::new("Stop").with_action("run.stop"))
            .add_item(MenuItem::new("Restart").with_action("run.restart")),

        // Terminal Menu
        DropdownMenu::new("Terminal")
            .add_item(MenuItem::new("New Terminal").with_action("terminal.new"))
            .add_item(MenuItem::new("Split Terminal").with_action("terminal.split").with_separator())
            .add_item(MenuItem::new("Kill Terminal").with_action("terminal.kill"))
            .add_item(MenuItem::new("Clear").with_action("terminal.clear")),

        // Help Menu
        DropdownMenu::new("Help")
            .add_item(MenuItem::new("Welcome").with_action("help.welcome"))
            .add_item(MenuItem::new("Documentation").with_action("help.docs").with_separator())
            .add_item(MenuItem::new("Keyboard Shortcuts").with_action("help.shortcuts"))
            .add_item(MenuItem::new("About MATRIX_IDE").with_action("help.about")),
    ]
}
