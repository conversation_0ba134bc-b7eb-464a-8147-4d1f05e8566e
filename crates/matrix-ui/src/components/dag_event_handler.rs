//! Handler di eventi per i componenti DAG
//!
//! Questo modulo fornisce handler per gli eventi DAG, permettendo
//! ai componenti UI di reagire ai cambiamenti nel DAG Engine.

use std::sync::Arc;
use async_trait::async_trait;

use matrix_core::CoreError;
use matrix_core::{Event, EventHandler};
use matrix_core::dag_engine::TaskState;

use crate::error::UiError;
use crate::components::dag_viewer::DagViewer;

/// Handler di eventi per il DagViewer
pub struct DagViewerEventHandler {
    /// DagViewer da aggiornare
    dag_viewer: Arc<DagViewer>,
}

impl DagViewerEventHandler {
    /// Crea un nuovo handler di eventi per il DagViewer
    pub fn new(dag_viewer: Arc<DagViewer>) -> Self {
        Self { dag_viewer }
    }
}

#[async_trait]
impl EventHandler for DagViewerEventHandler {
    async fn handle(&self, event: Event) -> Result<(), CoreError> {
        match event {
            // Quando viene aggiunto un task, aggiorna il layout
            Event::Custom { source, name, data } if source == "dag_engine" && name == "task_added" => {
                if let Some(graph_id) = data.get("graph_id").and_then(|v| v.as_str()) {
                    // TODO: Implement current_graph_id in simplified DagViewer
                    if true { // Placeholder: always execute for now
                        // TODO: Implement update_layout in simplified DagViewer
                        // self.dag_viewer.update_layout().map_err(|e| {
                        //     CoreError::EventHandlingError(format!("Failed to update DAG layout: {}", e))
                        // })?;
                    }
                }
            }

            // Quando un task cambia stato, aggiorna il grafo
            Event::Custom { source, name, data } if source == "dag_engine" && name == "task_completed" => {
                if let Some(graph_id) = data.get("graph_id").and_then(|v| v.as_str()) {
                    // TODO: Implement current_graph_id in simplified DagViewer
                    if true { // Placeholder: always execute for now
                        // TODO: Implement update_layout in simplified DagViewer
                        // self.dag_viewer.update_layout().map_err(|e| {
                        //     CoreError::EventHandlingError(format!("Failed to update DAG layout: {}", e))
                        // })?;
                    }
                }
            }

            // Gestisci eventi di annullamento dei task
            Event::Custom { source, name, data } if source == "dag_engine" && name == "task_cancelled" => {
                if let Some(graph_id) = data.get("graph_id").and_then(|v| v.as_str()) {
                    // TODO: Implement current_graph_id in simplified DagViewer
                    if true { // Placeholder: always execute for now
                        // TODO: Implement update_layout in simplified DagViewer
                        // self.dag_viewer.update_layout().map_err(|e| {
                        //     CoreError::EventHandlingError(format!("Failed to update DAG layout: {}", e))
                        // })?;
                    }
                }
            }

            // Gestisci eventi di inizio esecuzione DAG
            Event::DAGExecutionStarted { total_tasks, estimated_total_time_secs } => {
                // Qui potremmo aggiungere funzionalità di visualizzazione dello stato globale dell'esecuzione
                println!("DAG execution started: {} tasks, estimated time: {} seconds", 
                    total_tasks, estimated_total_time_secs);
            }

            // Gestisci eventi di completamento esecuzione DAG
            Event::DAGExecutionCompleted { completed_tasks, failed_tasks, total_time_ms } => {
                // Qui potremmo aggiornare indicatori di stato o notifiche
                println!("DAG execution completed: {} completed, {} failed, total time: {} ms", 
                    completed_tasks, failed_tasks, total_time_ms);

                // TODO: Implement update_layout in simplified DagViewer
                // self.dag_viewer.update_layout().map_err(|e| {
                //     CoreError::EventHandlingError(format!("Failed to update DAG layout: {}", e))
                // })?;
            }

            // Ignora altri eventi
            _ => {}
        }

        Ok(())
    }
}

/// Registra gli handler per gli eventi DAG
pub fn register_dag_event_handlers(
    viewer: Arc<DagViewer>, 
    event_bus: &Arc<matrix_core::EventBus>
) -> Result<(), UiError> {
    // Crea l'handler di eventi
    let handler = Arc::new(DagViewerEventHandler::new(viewer));

    // Crea il subscriber per gli eventi DAG
    let subscriber = Arc::new(matrix_core::EventSubscriber::new(
        vec![
            "dag.started",
            "dag.completed",
            "custom", // Per catturare gli eventi custom del DAG Engine
        ],
        handler,
    ));

    // Registra il subscriber nell'event bus
    event_bus.subscribe(subscriber).map_err(|e| {
        UiError::EventBusError(format!("Failed to subscribe to DAG events: {}", e))
    })?;

    Ok(())
}
