//! Componente input di testo personalizzato
//!
//! Questo modulo fornisce un input di testo personalizzato per l'IDE MATRIX.

use std::sync::Arc;
use floem::reactive::{RwSignal, SignalGet, SignalUpdate};
use floem::{View, IntoView, views::{text_input, h_stack, label, Decorators}};
use floem::style::Style;
use floem::text::Style as FontStyle;
use floem::peniko::Color;
use floem::text::Weight;
use crate::theme::ThemeManager;

/// Crea un input di testo personalizzato
pub fn matrix_text_input(
    theme_manager: Arc<ThemeManager>,
    value: RwSignal<String>,
    label_text: Option<&str>,
    placeholder: Option<&str>,
) -> Box<dyn View> {
    let theme = theme_manager.get_active_theme().unwrap_or_default();

    let input = text_input(value)
        .placeholder(placeholder.unwrap_or("").to_string())
        .style(move |s| {
            s.width_full()
             .padding(8.0)
             .border(theme.borders.width_thin)
             .border_color(theme.colors.border)
             .border_radius(theme.borders.radius_medium)
             .background(theme.colors.background_secondary)
             .color(theme.colors.text)
             .hover(|s| s.border_color(theme.colors.accent))
             .focus(|s| s.border_color(theme.colors.accent))
             .active(|s| s.border_color(theme.colors.accent))
             .disabled(|s| s.color(theme.colors.text.multiply_alpha(0.6)))
        });

    if let Some(label_str) = label_text {
        // Crea un h_stack con etichetta se fornita
        let label_string = label_str.to_string(); // Converti in String owned
        h_stack((
            label(move || label_string.clone())
                .style(|s| {
                    s.font_weight(Weight::MEDIUM)
                     .margin_right(8.0)
                     .min_width(100.0)
                }),
            input,
        ))
        .style(|s| s.width_full())
        .into_any()
    } else {
        // Altrimenti restituisci solo l'input
        input.into_any()
    }
}

/// Crea un input di testo per la ricerca
pub fn search_input(
    theme_manager: Arc<ThemeManager>,
    value: RwSignal<String>,
    placeholder: Option<&str>,
) -> impl View {
    let theme = theme_manager.get_active_theme().unwrap_or_default();

    text_input(value)
        .placeholder(placeholder.unwrap_or("Cerca...").to_string())
        .style(move |s| {
            s.width_full()
             .padding(8.0)
             .border(theme.borders.width_thin)
             .border_color(theme.colors.border)
             .border_radius(theme.borders.radius_medium)
             .background(theme.colors.background_secondary)
             .color(theme.colors.text)
             .hover(|s| s.border_color(theme.colors.accent))
             .focus(|s| s.border_color(theme.colors.accent))
             .active(|s| s.border_color(theme.colors.accent))
             .disabled(|s| s.background(theme.colors.background.multiply_alpha(0.6)))
        })
}

/// Crea un textarea (input multilinea)
pub fn matrix_textarea(
    theme_manager: Arc<ThemeManager>,
    value: RwSignal<String>,
    rows: usize,
    placeholder: Option<&str>,
) -> impl View {
    let theme = theme_manager.get_active_theme().unwrap_or_default();

    // In Floem, non esiste un textarea nativo, quindi usiamo un text_input
    // e aggiungiamo alcuni stili per farlo sembrare multilinea
    let height = (rows as f64 * 24.0) as f32; // Altezza approssimativa basata sul numero di righe

    text_input(value)
        .placeholder(placeholder.unwrap_or("").to_string())
        .style(move |s| {
            s.width_full()
             .height(height)
             .padding(8.0)
             .border(theme.borders.width_thin)
             .border_color(theme.colors.border)
             .border_radius(theme.borders.radius_medium)
             .background(theme.colors.background_secondary)
             .color(theme.colors.text)
             .hover(|s| s.border_color(theme.colors.accent))
             .focus(|s| s.border_color(theme.colors.accent))
             .active(|s| s.border_color(theme.colors.accent))
             .disabled(|s| s.background(theme.colors.background.multiply_alpha(0.6)))
        })
}

/// Crea un campo di testo con etichetta e messaggio di errore opzionale
pub fn form_field(
    theme_manager: Arc<ThemeManager>,
    value: RwSignal<String>,
    label_text: &str,
    placeholder: Option<&str>,
    error: RwSignal<Option<String>>,
) -> impl View {
    let theme = theme_manager.get_active_theme().unwrap_or_default();

    floem::views::v_stack((
        label({
            let label_text = label_text.to_string();
            move || label_text.clone()
        })
            .style(move |s| {
                s.font_weight(Weight::MEDIUM)
                 .margin_bottom(4.0)
                 .color(theme.colors.text)
            }),

        text_input(value)
            .placeholder(placeholder.unwrap_or("").to_string())
            .style(move |s| {
                let border_color = if error.get().is_some() {
                    theme.colors.error
                } else {
                    theme.colors.border
                };

                s.width_full()
                 .padding(8.0)
                 .border(theme.borders.width_thin)
                 .border_color(border_color)
                 .border_radius(theme.borders.radius_medium)
                 .background(theme.colors.background_secondary)
                 .color(theme.colors.text)
                 .hover(|s| {
                     if error.get().is_some() {
                         s.border_color(theme.colors.error)
                     } else {
                         s.border_color(theme.colors.accent)
                     }
                 })
                 .focus(|s| {
                     if error.get().is_some() {
                         s.border_color(theme.colors.error)
                     } else {
                         s.border_color(theme.colors.accent)
                     }
                 })
                 .disabled(|s| s.background(theme.colors.background.multiply_alpha(0.6)))
            }),

        label(move || {
            error.get().unwrap_or_default()
        })
        .style(move |s| {
            s.color(theme.colors.error)
             .font_style(FontStyle::Italic)
             .font_size(theme.font_sizes.xs)
             .margin_top(4.0)
             .height(if error.get().is_some() { 20.0 } else { 0.0 })
             .apply(if error.get().is_some() {
                 floem::style::Style::new().color(Color::rgb8(255, 0, 0))
             } else {
                 floem::style::Style::new().color(Color::TRANSPARENT)
             })
        }),
    ))
    .style(|s| s.width_full().margin_bottom(16.0))
}
