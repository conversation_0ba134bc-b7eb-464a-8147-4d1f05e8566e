//! Visualizzatore DAG Semplificato per MATRIX_IDE (Floem 0.2 compatible)
//!
//! Versione semplificata del visualizzatore DAG per garantire la compilazione
//! con Floem 0.2. Implementazione completa sarà aggiunta incrementalmente.

use std::sync::Arc;
use floem::peniko::Color;
use floem::reactive::{create_rw_signal, RwSignal, SignalGet, SignalUpdate};
use floem::{View, views::{container, label, v_stack, h_stack, button, Decorators}};

use matrix_core::Engine as CoreEngine;
use crate::theme::ThemeManager;
use crate::error::UiError;

/// Visualizzatore DAG semplificato
pub struct DagViewer {
    core: Arc<CoreEngine>,
    theme_manager: Arc<ThemeManager>,
    current_graph_id: RwSignal<Option<String>>,
    selected_node: RwSignal<Option<String>>,
}

impl DagViewer {
    /// Crea un nuovo visualizzatore DAG
    pub fn new(core: Arc<CoreEngine>, theme_manager: Arc<ThemeManager>) -> Result<Arc<Self>, UiError> {
        let current_graph_id = create_rw_signal(None);
        let selected_node = create_rw_signal(None);

        Ok(Arc::new(Self {
            core,
            theme_manager,
            current_graph_id,
            selected_node,
        }))
    }

    /// Crea la vista del visualizzatore DAG
    pub fn create_view(&self) -> impl View {
        let current_graph_id = self.current_graph_id;
        let selected_node = self.selected_node;

        container(
            v_stack((
                // Header
                label(move || {
                    if let Some(graph_id) = current_graph_id.get() {
                        format!("DAG Viewer - Graph: {}", graph_id)
                    } else {
                        "DAG Viewer - No graph selected".to_string()
                    }
                })
                .style(|s| s.font_size(16.0).font_bold().padding(16)),

                // Status
                label(move || {
                    if let Some(node_id) = selected_node.get() {
                        format!("Selected node: {}", node_id)
                    } else {
                        "No node selected".to_string()
                    }
                })
                .style(|s| s.padding(8).color(Color::rgb8(0xA0, 0xA0, 0xA0))),

                // Placeholder for graph visualization
                container(
                    label(|| "Graph visualization will be implemented here")
                        .style(|s| s.font_size(14.0).color(Color::rgb8(0x80, 0x80, 0x80)))
                )
                .style(|s| {
                    s.size_full()
                     .justify_center()
                     .items_center()
                     .background(Color::rgb8(0x18, 0x18, 0x18))
                     .border(1)
                     .border_color(Color::rgb8(0x30, 0x30, 0x30))
                     .border_radius(4)
                     .margin(16)
                }),

                // Controls
                h_stack((
                    button(label(|| "Refresh".to_string()))
                        .action(|| {})
                        .style(|s| s.margin(4).padding(8)),
                    button(label(|| "Reset View".to_string()))
                        .action(|| {})
                        .style(|s| s.margin(4).padding(8)),
                    button(label(|| "Export".to_string()))
                        .action(|| {})
                        .style(|s| s.margin(4).padding(8)),
                ))
                .style(|s| s.justify_center().padding(16)),
            ))
        )
        .style(|s| {
            s.size_full()
             .background(Color::rgb8(0x21, 0x21, 0x21))
        })
    }

    /// Ottiene l'ID del grafo corrente
    pub fn current_graph_id(&self) -> Option<String> {
        self.current_graph_id.get()
    }

    /// Imposta il grafo corrente
    pub fn set_current_graph(&self, graph_id: Option<String>) {
        self.current_graph_id.set(graph_id);
    }

    /// Ottiene il nodo selezionato
    pub fn selected_node(&self) -> Option<String> {
        self.selected_node.get()
    }

    /// Seleziona un nodo
    pub fn select_node(&self, node_id: Option<String>) {
        self.selected_node.set(node_id);
    }

    /// Imposta il grafo (placeholder per compatibilità)
    pub fn set_graph(&self, _graph_data: serde_json::Value) -> Result<(), crate::error::UiError> {
        // TODO: Implement graph setting in simplified DagViewer
        Ok(())
    }
}

impl Clone for DagViewer {
    fn clone(&self) -> Self {
        Self {
            core: self.core.clone(),
            theme_manager: self.theme_manager.clone(),
            current_graph_id: self.current_graph_id,
            selected_node: self.selected_node,
        }
    }
}
