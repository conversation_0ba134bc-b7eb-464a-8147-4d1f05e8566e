//! Layout principale dell'applicazione
//!
//! Questo modulo definisce il layout principale dell'applicazione UI,
//! integrando tutti i componenti in un'interfaccia coesa.

use std::sync::Arc;
use floem::View;
use floem::views::{container, h_stack, v_stack, Decorators};
use floem::style::Style;
use crate::{
    error::UiError,
    theme::ThemeManager,
    panels::PanelManager,
};

/// Layout principale dell'applicazione
pub struct MainLayout {
    /// Gestore dei pannelli
    panel_manager: Arc<PanelManager>,

    /// Gestore dei temi
    theme_manager: Arc<ThemeManager>,
}

impl MainLayout {
    /// Crea un nuovo layout principale
    pub fn new(
        panel_manager: Arc<PanelManager>,
        theme_manager: Arc<ThemeManager>,
    ) -> Result<Self, UiError> {
        Ok(Self {
            panel_manager,
            theme_manager,
        })
    }

    /// Costruisce il layout principale
    pub fn build(&self) -> Result<impl View, UiError> {
        let theme = self.theme_manager.get_active_theme()?;

        // Ottiene i pannelli dal gestore
        let left_panel = self.panel_manager.get_left_panel()?;
        let right_panel = self.panel_manager.get_right_panel()?;
        let bottom_panel = self.panel_manager.get_bottom_panel()?;
        let editor_panel = self.panel_manager.get_editor_panel()?;

        // Crea il layout principale
        let main_layout = v_stack((
            // Main horizontal layout
            h_stack((
                // Pannello sinistro
                container(left_panel)
                    .style({
                        let border_color = theme.colors.border;
                        move |s| {
                            s.width(250.0)
                                .height_full()
                                .border_right(1.0)
                                .border_color(border_color)
                        }
                    }),

                // Area centrale (editor)
                container(editor_panel)
                    .style(|s| {
                        s.flex_grow(1.0)
                            .height_full()
                    }),

                // Pannello destro
                container(right_panel)
                    .style({
                        let border_color = theme.colors.border;
                        move |s| {
                            s.width(300.0)
                                .height_full()
                                .border_left(1.0)
                                .border_color(border_color)
                        }
                    }),
            ))
            .style(|s| s.flex_grow(1.0).width_full()),

            // Pannello inferiore
            container(bottom_panel)
                .style({
                    let border_color = theme.colors.border;
                    move |s| {
                        s.height(200.0)
                            .width_full()
                            .border_top(1.0)
                            .border_color(border_color)
                    }
                }),
        ))
        .style({
            let background_color = theme.colors.background;
            move |s| {
                s.size_full()
                    .background(background_color)
            }
        });

        Ok(main_layout)
    }
}
