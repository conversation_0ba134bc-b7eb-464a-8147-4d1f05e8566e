//! # Matrix UI
//! 
//! Layer UI di MATRIX IDE, che integra Lapce precompilato con Floem 0.2
//! per creare un'interfaccia utente moderna e reattiva.

mod app;
mod error;
mod theme;
mod components;
mod layout;
mod floem_widgets;
mod lapce_bridge;
mod floem_bridge;
mod editor;
mod panels;
mod plugin_host;
mod plugin_integrator;

pub use app::App;
pub use error::UiError;
pub use theme::{Theme, ThemeManager};
pub use floem_bridge::{FloemBridge, UiComponent};
pub use editor::{Editor, lapce::{LapceEditor, LapceConfig, WrapMode}};
pub use panels::*;
pub use plugin_host::PluginHost;
pub use plugin_integrator::{PluginHostIntegrator, PluginIntegrationError};

/// Versione del UI Layer
pub const VERSION: &str = env!("CARGO_PKG_VERSION");

/// Inizializza il UI Layer con la configurazione predefinita
pub fn init() -> Result<App, error::UiError> {
    App::new()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_app_creation() {
        let app = init();
        assert!(app.is_ok());
    }
}