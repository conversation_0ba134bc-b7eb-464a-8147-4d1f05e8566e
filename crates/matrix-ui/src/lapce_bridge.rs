//! Modulo di integrazione con l'editor Lapce
//!
//! Questo modulo fornisce l'integrazione tra il nostro IDE e l'editor La<PERSON><PERSON>.
//! Consente di incorporare l'editor La<PERSON>ce nella nostra interfaccia Floem.

use std::sync::{Arc, Mutex, RwLock};
use std::path::PathBuf;

use floem::{View, views::Decorators};
use floem::reactive::{RwSignal, create_rw_signal, SignalGet, SignalUpdate};
use matrix_core::{Engine as CoreEngine, EventBus};
use crate::theme::ThemeManager;
use crate::error::UiError;
use crate::editor::LapceEditor;

/// Stato del bridge con Lapce
pub struct LapceState {
    /// File attualmente aperto
    current_file: RwSignal<Option<PathBuf>>,

    /// Lista dei file aperti
    open_files: RwSignal<Vec<PathBuf>>,

    /// Contenuto non salvato (per file virtuale)
    unsaved_content: RwSignal<Option<String>>,

    /// È in modalità di sola lettura?
    read_only: RwSignal<bool>,
}

/// Evento quando il contenuto di un file è stato modificato
pub struct FileContentChangedEvent {
    /// Percorso del file modificato
    pub path: PathBuf,

    /// Nuovo contenuto
    pub content: String,
}

/// Integrazione con Lapce
pub struct LapceIntegration {
    /// Core Engine
    core: Arc<CoreEngine>,

    /// Gestore dei temi
    theme_manager: Arc<ThemeManager>,

    /// Stato dell'integrazione
    state: LapceState,

    /// Istanza dell'editor Lapce
    editor: Arc<RwLock<Option<LapceEditor>>>,
}

impl LapceIntegration {
    /// Crea una nuova integrazione con Lapce
    pub fn new(
        core: Arc<CoreEngine>,
        theme_manager: Arc<ThemeManager>,
    ) -> Result<Self, UiError> {
        let state = LapceState {
            current_file: create_rw_signal(None),
            open_files: create_rw_signal(Vec::new()),
            unsaved_content: create_rw_signal(None),
            read_only: create_rw_signal(false),
        };

        Ok(Self {
            core,
            theme_manager,
            state,
            editor: Arc::new(RwLock::new(None)),
        })
    }

    /// Inizializza l'integrazione con Lapce
    pub fn initialize(&self) -> Result<(), UiError> {
        // Qui verrebbe inizializzato il processo Lapce con tutte le configurazioni necessarie
        // Per ora è un mock

        // Sottoscrive agli eventi rilevanti
        self.subscribe_to_events()?;

        Ok(())
    }

    /// Sottoscrive agli eventi rilevanti
    fn subscribe_to_events(&self) -> Result<(), UiError> {
        // Qui verrebbero registrati i gestori di eventi per intercettare
        // gli eventi di Lapce e propagarli al nostro sistema

        // Per ora, gestiamo solo l'evento di selezione del file dal file explorer
        let event_bus = self.core.event_bus();
        let current_file = self.state.current_file.clone();
        let open_files = self.state.open_files.clone();

        // TODO: Reimplementare la subscription degli eventi con la nuova API
        // L'API di subscribe è cambiata in Floem 0.2 e richiede un EventSubscriber
        // Per ora commentiamo questo codice per far compilare il progetto
        /*
        event_bus.subscribe(Arc::new(FileSelectedEventSubscriber {
            open_files: open_files.clone(),
            current_file: current_file.clone(),
        }))?;
        */

        Ok(())
    }

    /// Crea una vista dell'editor Lapce utilizzando la nuova implementazione LapceEditor
    pub fn create_editor_view(&self) -> Result<impl View, UiError> {
        let current_file = self.state.current_file.get();
        let theme = self.theme_manager.get_active_theme()?;
        
        // Ottiene l'accesso all'editor
        let mut editor_guard = self.editor.write().map_err(|_| {
            UiError::LockError("Failed to acquire write lock on editor".to_string())
        })?;
        
        // Se non c'è ancora un'istanza dell'editor, creane una nuova
        if editor_guard.is_none() {
            *editor_guard = Some(LapceEditor::new(theme, None)?);
        } else if let Some(editor) = &mut *editor_guard {
            // Se c'è un'istanza e un nuovo file è stato selezionato, aprilo
            if let Some(path) = &current_file {
                if editor.get_current_file().map_or(true, |f| f != *path) {
                    editor.open_file(path.clone())?;
                }
            }
        }
        
        // Crea la vista dell'editor
        if let Some(editor) = &mut *editor_guard {
            let placeholder = if current_file.is_none() {
                "// Seleziona un file dall'esploratore per aprirlo qui."
            } else {
                ""
            };
            
            // Rilascia il lock prima di restituire la vista
            let view = editor.create_editor_view(placeholder)?;
            drop(editor_guard);
            
            Ok(floem::views::v_stack((
                // Barra superiore con informazioni sul file
                floem::views::h_stack((
                    floem::views::label(move || {
                        if let Some(path) = self.state.current_file.get() {
                            format!("Editor - {}", path.display())
                        } else {
                            "Editor - Nessun file aperto".to_string()
                        }
                    })
                    .style(|s| s.font_bold().margin_bottom(8)),

                    floem::views::label(move || {
                        if self.state.read_only.get() {
                            "[Sola lettura]".to_string()
                        } else {
                            "".to_string()
                        }
                    })
                    .style(|s| s.margin_left(8).color(floem::peniko::Color::rgb8(0xA0, 0xA0, 0xA0))),
                ))
                .style(|s| s.width_full().padding(8)),
                
                // Vista dell'editor
                view,
            ))
            .style(|s| s.width_full().height_full()))
        } else {
            // Fallback se l'editor non può essere creato
            Ok(floem::views::stack((
                floem::views::label(|| "Errore nell'inizializzazione dell'editor")
                    .style(|s| s.width_full().height_full().padding(16)),
            )))
        }
    }

    /// Apre un file nell'editor
    pub fn open_file(&self, path: PathBuf) -> Result<(), UiError> {
        // Verifica se il file esiste
        if !path.exists() || !path.is_file() {
            return Err(UiError::FileError(format!(
                "Il file {} non esiste o non è un file valido",
                path.display()
            )));
        }

        // Aggiunge alla lista dei file aperti
        self.state.open_files.update(|files| {
            if !files.contains(&path) {
                files.push(path.clone());
            }
        });

        // Imposta come file corrente
        self.state.current_file.set(Some(path.clone()));

        // Reset modalità di sola lettura
        self.state.read_only.set(false);
        
        // Aggiorna l'editor se esiste
        let mut editor_guard = self.editor.write().map_err(|_| {
            UiError::LockError("Failed to acquire write lock on editor".to_string())
        })?;
        
        if let Some(editor) = &mut *editor_guard {
            editor.open_file(path)?;
        }
        
        Ok(())
    }

    /// Ottiene il file attualmente aperto
    pub fn current_file(&self) -> Option<PathBuf> {
        self.state.current_file.get()
    }

    /// Ottiene la lista dei file aperti
    pub fn open_files(&self) -> Vec<PathBuf> {
        self.state.open_files.get()
    }

    /// Salva il file corrente
    pub fn save_current_file(&self) -> Result<(), UiError> {
        if let Some(_) = self.state.current_file.get() {
            // Salva il file utilizzando l'editor
            let mut editor_guard = self.editor.write().map_err(|_| {
                UiError::LockError("Failed to acquire write lock on editor".to_string())
            })?;
            
            if let Some(editor) = &mut *editor_guard {
                editor.save()?;
            } else {
                return Err(UiError::EditorError("L'editor non è inizializzato".to_string()));
            }
        } else {
            return Err(UiError::EditorError("Nessun file aperto da salvare".to_string()));
        }

        Ok(())
    }

    /// Chiude l'integrazione con Lapce
    pub fn shutdown(&self) -> Result<(), UiError> {
        // Salva eventuali modifiche non salvate
        if let Some(editor) = &*self.editor.read().map_err(|_| {
            UiError::LockError("Failed to acquire read lock on editor".to_string())
        })? {
            if editor.is_modified() {
                // Prova a salvare il file se modificato
                let mut editor_guard = self.editor.write().map_err(|_| {
                    UiError::LockError("Failed to acquire write lock on editor".to_string())
                })?;
                
                if let Some(editor) = &mut *editor_guard {
                    if let Err(e) = editor.save() {
                        log::warn!("Impossibile salvare il file durante lo shutdown: {}", e);
                    }
                }
            }
        }
        
        // Rilascia le risorse dell'editor
        *self.editor.write().map_err(|_| {
            UiError::LockError("Failed to acquire write lock on editor".to_string())
        })? = None;
        
        Ok(())
    }
}
