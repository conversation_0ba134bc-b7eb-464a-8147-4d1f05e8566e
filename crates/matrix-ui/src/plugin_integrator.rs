//! Integrazione tra PluginHost e PluginLoader

use std::path::{Path, PathBuf};
use std::sync::{Arc, Mutex};

use matrix_core::{Plugin, CoreError};
use matrix_plugin_loader::{
    Manifest, 
    PluginRegistry, 
    DependencyResolver,
    hot_reload_plugin,
};

/// Errore di integrazione plugin
#[derive(Debug, thiserror::Error)]
pub enum PluginIntegrationError {
    #[error("Errore di caricamento plugin: {0}")]
    LoadError(String),

    #[error("Errore del registry: {0}")]
    RegistryError(#[from] matrix_plugin_loader::RegistryError),

    #[error("Errore di dipendenza: {0}")]
    DependencyError(#[from] matrix_plugin_loader::DependencyError),

    #[error("Errore del plugin: {0}")]
    PluginError(#[from] CoreError),

    #[error("Errore I/O: {0}")]
    IoError(#[from] std::io::Error),

    #[error("Errore di permesso: {0}")]
    PermissionError(String),
}

/// Tipo di risultato per le operazioni di integrazione
pub type PluginIntegrationResult<T> = Result<T, PluginIntegrationError>;

/// Integratore Plugin-Host
pub struct PluginHostIntegrator {
    /// Plugin host
    plugin_host: Arc<super::PluginHost>,

    /// Registry dei plugin
    registry: PluginRegistry,

    /// Resolver delle dipendenze
    resolver: DependencyResolver,

    /// Checker dei permessi
    #[cfg(feature = "perm-debug")]
    permission_checker: Arc<Mutex<PermissionChecker>>,
}

impl PluginHostIntegrator {
    /// Crea un nuovo integratore
    pub fn new(plugin_host: Arc<super::PluginHost>, registry_path: impl AsRef<Path>) -> PluginIntegrationResult<Self> {
        let registry = PluginRegistry::new(registry_path)?;
        let resolver = DependencyResolver::new(registry.clone());

        #[cfg(feature = "perm-debug")]
        let permission_checker = Arc::new(Mutex::new(PermissionChecker::new()));

        Ok(Self {
            plugin_host,
            registry,
            resolver,
            #[cfg(feature = "perm-debug")]
            permission_checker,
        })
    }

    /// Carica tutti i plugin dal registry
    pub fn load_all_plugins(&mut self) -> PluginIntegrationResult<()> {
        // Costruisci il grafo delle dipendenze
        self.resolver.build_dependency_graph()?;

        // Verifica i cicli e la compatibilità
        if let Err(e) = self.resolver.check_for_cycles() {
            log::warn!("Rilevato un ciclo di dipendenze: {}", e);
            // Continua comunque, caricheremo ciò che è possibile
        }

        if let Err(e) = self.resolver.check_version_compatibility() {
            log::warn!("Rilevata incompatibilità di versioni: {}", e);
            // Continua comunque, caricheremo ciò che è possibile
        }

        // Ottieni l'ordine di caricamento
        let load_order = match self.resolver.get_load_order() {
            Ok(order) => order,
            Err(e) => {
                log::warn!("Impossibile determinare l'ordine di caricamento: {}", e);
                // Carica almeno i plugin senza dipendenze
                let plugins = self.registry.get_all_plugins();
                plugins.iter().map(|p| p.id.clone()).collect()
            }
        };

        log::info!("Caricamento di {} plugin nell'ordine: {:?}", load_order.len(), load_order);

        // Carica i plugin nell'ordine corretto
        for plugin_id in load_order {
            match self.load_plugin(&plugin_id) {
                Ok(_) => log::info!("Plugin {} caricato con successo", plugin_id),
                Err(e) => log::error!("Errore nel caricamento del plugin {}: {}", plugin_id, e),
            }
        }

        Ok(())
    }

    /// Carica un singolo plugin
    pub fn load_plugin(&self, plugin_id: &str) -> PluginIntegrationResult<()> {
        // Ottieni le informazioni sul plugin
        let plugin_info = self.registry.get_plugin_info(plugin_id)?;

        // Carica il manifest per i controlli di permesso
        let manifest = Manifest::load(&plugin_info.manifest_path)?;

        // Carica la libreria dinamica
        let dylib_path = &plugin_info.binary_path;
        log::debug!("Caricamento della libreria {:?}", dylib_path);

        // Carica la libreria dinamica
        let library = unsafe { 
            libloading::Library::new(dylib_path)
                .map_err(|e| PluginIntegrationError::LoadError(e.to_string()))?
        };

        // Ottieni il simbolo di creazione
        let create_fn: libloading::Symbol<unsafe fn() -> *mut dyn Plugin> = unsafe {
            library.get(b"create_plugin")
                .map_err(|e| PluginIntegrationError::LoadError(e.to_string()))?
        };

        // Crea il plugin
        let plugin_ptr = unsafe { create_fn() };
        let plugin_box = unsafe { Box::from_raw(plugin_ptr) };

        // Verifica i permessi
        #[cfg(feature = "perm-debug")]
        {
            let mut checker = self.permission_checker.lock().unwrap();
            checker.register_plugin(plugin_id, &manifest.permissions);
        }

        // Carica il plugin nel host
        self.plugin_host.load_plugin(Arc::new(plugin_box))?;

        Ok(())
    }

    /// Ricarica un plugin
    pub fn reload_plugin(&mut self, plugin_id: &str) -> PluginIntegrationResult<()> {
        // Scarica il plugin dal host
        self.plugin_host.unload_plugin(plugin_id)?;

        // Hot-reload del plugin nel registry
        hot_reload_plugin(&mut self.registry, plugin_id)?;

        // Ricarica il plugin
        self.load_plugin(plugin_id)?;

        log::info!("Plugin {} ricaricato con successo", plugin_id);

        Ok(())
    }

    /// Ottieni il registry
    pub fn registry(&self) -> &PluginRegistry {
        &self.registry
    }

    /// Ottieni il registry mutabile
    pub fn registry_mut(&mut self) -> &mut PluginRegistry {
        &mut self.registry
    }
}

/// Checker dei permessi
#[cfg(feature = "perm-debug")]
pub struct PermissionChecker {
    /// Permessi dei plugin
    plugin_permissions: std::collections::HashMap<String, matrix_plugin_loader::Permissions>,
}

#[cfg(feature = "perm-debug")]
impl PermissionChecker {
    /// Crea un nuovo checker
    pub fn new() -> Self {
        Self {
            plugin_permissions: std::collections::HashMap::new(),
        }
    }

    /// Registra un plugin
    pub fn register_plugin(&mut self, plugin_id: &str, permissions: &matrix_plugin_loader::Permissions) {
        self.plugin_permissions.insert(plugin_id.to_string(), permissions.clone());
    }

    /// Verifica un permesso
    pub fn check_permission(&self, plugin_id: &str, category: &str, permission: &str) -> Result<(), PluginIntegrationError> {
        if let Some(permissions) = self.plugin_permissions.get(plugin_id) {
            let has_permission = match category {
                "filesystem" => permissions.filesystem.contains(&permission.to_string()),
                "network" => permissions.network.contains(&permission.to_string()),
                "ui" => permissions.ui.contains(&permission.to_string()),
                _ => false,
            };

            if !has_permission {
                return Err(PluginIntegrationError::PermissionError(
                    format!("Il plugin {} non ha il permesso {} di {}", plugin_id, permission, category)
                ));
            }
        } else {
            return Err(PluginIntegrationError::PermissionError(
                format!("Plugin {} non registrato", plugin_id)
            ));
        }

        Ok(())
    }
}
